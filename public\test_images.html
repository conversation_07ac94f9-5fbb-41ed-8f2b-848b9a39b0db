<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار الصور</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            padding: 20px;
            background-color: #f5f5f5;
        }
        .test-container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .image-test {
            margin: 10px 0;
            padding: 10px;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .image-test img {
            max-width: 200px;
            max-height: 100px;
            border: 1px solid #ccc;
        }
        .success { color: green; }
        .error { color: red; }
    </style>
</head>
<body>
    <h1>🧪 اختبار عرض الصور في النظام</h1>
    
    <div class="test-container">
        <h2>📋 اختبار شعارات الشركة</h2>
        
        <div class="image-test">
            <h3>شعار الشركة (الداكن)</h3>
            <p><strong>الرابط:</strong> <code>http://localhost/up20251/storage/logo/logo-dark.png</code></p>
            <img src="http://localhost/up20251/storage/logo/logo-dark.png" alt="شعار الشركة الداكن" 
                 onload="this.nextElementSibling.innerHTML='✅ تم تحميل الصورة بنجاح'; this.nextElementSibling.className='success';"
                 onerror="this.nextElementSibling.innerHTML='❌ فشل في تحميل الصورة'; this.nextElementSibling.className='error';">
            <p class="status">⏳ جاري التحميل...</p>
        </div>
        
        <div class="image-test">
            <h3>شعار الشركة (الفاتح)</h3>
            <p><strong>الرابط:</strong> <code>http://localhost/up20251/storage/logo/logo-light.png</code></p>
            <img src="http://localhost/up20251/storage/logo/logo-light.png" alt="شعار الشركة الفاتح"
                 onload="this.nextElementSibling.innerHTML='✅ تم تحميل الصورة بنجاح'; this.nextElementSibling.className='success';"
                 onerror="this.nextElementSibling.innerHTML='❌ فشل في تحميل الصورة'; this.nextElementSibling.className='error';">
            <p class="status">⏳ جاري التحميل...</p>
        </div>
    </div>
    
    <div class="test-container">
        <h2>👤 اختبار صور المستخدمين</h2>
        
        <div class="image-test">
            <h3>صورة المستخدم الافتراضية</h3>
            <p><strong>الرابط:</strong> <code>http://localhost/up20251/storage/avatar/avatar.png</code></p>
            <img src="http://localhost/up20251/storage/avatar/avatar.png" alt="صورة المستخدم الافتراضية"
                 onload="this.nextElementSibling.innerHTML='✅ تم تحميل الصورة بنجاح'; this.nextElementSibling.className='success';"
                 onerror="this.nextElementSibling.innerHTML='❌ فشل في تحميل الصورة'; this.nextElementSibling.className='error';">
            <p class="status">⏳ جاري التحميل...</p>
        </div>
    </div>
    
    <div class="test-container">
        <h2>📊 النتائج</h2>
        <p>إذا ظهرت جميع الصور أعلاه بنجاح، فإن مشكلة عرض الصور قد تم حلها بالكامل.</p>
        <p><strong>ملاحظة:</strong> يمكنك حذف هذا الملف بعد التأكد من عمل الصور: <code>public/test_images.html</code></p>
    </div>
</body>
</html>
