# 🔧 تحديث صفحة إعدادات النظام (System Settings)

## 📋 التحديثات المطبقة

### ✅ **إنشاء صفحة إعدادات النظام الكاملة**
تم إنشاء ملف `resources/views/settings/index.blade.php` مع جميع الإعدادات المطلوبة.

### 🎯 **الأقسام المضافة:**

#### 1. **Brand Settings (إعدادات العلامة التجارية)**
- **Logo Dark** - شعار النظام للوضع المظلم
- **Logo Light** - شعار النظام للوضع الفاتح
- **Favicon** - أيقونة الموقع
- **Application Name** - اسم التطبيق
- **Application Description** - وصف التطبيق
- **Default Language** - اللغة الافتراضية
- **Enable RTL** - تفعيل الكتابة من اليمين لليسار
- **Enable Landing Page** - تفعيل الصفحة الرئيسية
- **Enable User Registration** - السماح بالتسجيل
- **Email Verification** - التحقق من البريد الإلكتروني

#### 2. **System Settings (إعدادات النظام الأساسية)**
- **اسم التطبيق** (`title_text`) - اسم النظام الذي يظهر في الواجهة
- **وصف التطبيق** (`footer_text`) - وصف مختصر للنظام
- **اللغة الافتراضية** (`default_language`) - اللغة الأساسية للنظام
- **المنطقة الزمنية** (`timezone`) - المنطقة الزمنية للنظام
- **العملة** (`site_currency`) - رمز العملة الأساسية
- **رمز العملة** (`site_currency_symbol`) - رمز العملة ($, €, ريال)
- **موضع رمز العملة** (`site_currency_symbol_position`) - قبل أو بعد المبلغ
- **عدد الخانات العشرية** (`decimal_number`) - دقة الأرقام العشرية
- **تنسيق التاريخ** (`site_date_format`) - شكل عرض التاريخ
- **تنسيق الوقت** (`site_time_format`) - شكل عرض الوقت
- **تفعيل التسجيل** (`enable_signup`) - السماح للمستخدمين بالتسجيل
- **التحقق من البريد الإلكتروني** (`email_verification`) - تفعيل التحقق

#### 3. **Email Settings (إعدادات البريد الإلكتروني)**
- **Mail Driver** - نوع خدمة البريد (SMTP)
- **Mail Host** - خادم البريد الإلكتروني
- **Mail Port** - منفذ الاتصال
- **Mail Username** - اسم المستخدم
- **Mail Password** - كلمة المرور
- **Mail Encryption** - نوع التشفير (TLS/SSL)
- **Mail From Address** - عنوان المرسل
- **Mail From Name** - اسم المرسل

#### 4. **Payment Settings (إعدادات الدفع المتقدمة)**
- **عملة الدفع** (`currency`) - العملة المستخدمة في المدفوعات
- **تفعيل Stripe** (`enable_stripe`) - تفعيل بوابة Stripe
- **مفتاح Stripe العام** (`stripe_key`) - المفتاح العام لـ Stripe
- **مفتاح Stripe السري** (`stripe_secret`) - المفتاح السري لـ Stripe
- **تفعيل PayPal** (`enable_paypal`) - تفعيل بوابة PayPal
- **وضع PayPal** (`paypal_mode`) - وضع الاختبار أو المباشر
- **معرف عميل PayPal** (`paypal_client_id`) - معرف العميل
- **مفتاح PayPal السري** (`paypal_secret_key`) - المفتاح السري

#### 5. **Storage Settings (إعدادات التخزين الشاملة)**
- **مزود التخزين** (`storage_setting`) - محلي، AWS S3، أو Wasabi
- **حد الرفع الأقصى** (`local_storage_max_upload_size`) - حجم الملف الأقصى
- **أنواع الملفات المسموحة** (`local_storage_validation`) - امتدادات الملفات

##### **إعدادات AWS S3:**
- **مفتاح الوصول** (`s3_key`)
- **المفتاح السري** (`s3_secret`)
- **المنطقة** (`s3_region`)
- **اسم الحاوية** (`s3_bucket`)

##### **إعدادات Wasabi:**
- **مفتاح الوصول** (`wasabi_key`)
- **المفتاح السري** (`wasabi_secret`)
- **المنطقة** (`wasabi_region`)
- **اسم الحاوية** (`wasabi_bucket`)

#### 6. **Pusher Settings (إعدادات الإشعارات الفورية)**
- **Pusher App ID** - معرف تطبيق Pusher
- **Pusher App Key** - مفتاح التطبيق
- **Pusher App Secret** - المفتاح السري
- **Pusher App Cluster** - المجموعة الجغرافية

#### 7. **reCAPTCHA Settings (إعدادات الحماية)**
- **Enable reCAPTCHA** - تفعيل حماية reCAPTCHA
- **reCAPTCHA Secret Key** - المفتاح السري
- **reCAPTCHA Site Key** - مفتاح الموقع

#### 8. **SEO Settings (إعدادات محركات البحث)**
- **Meta Title** - عنوان الصفحة في محركات البحث
- **Meta Description** - وصف الصفحة
- **Meta Image** - صورة الصفحة للمشاركة

#### 9. **Cookie Settings (إعدادات الكوكيز)**
- **Enable Cookie Consent** - تفعيل موافقة الكوكيز (GDPR)
- **Cookie Text** - نص رسالة الكوكيز

#### 10. **Cache Settings (إعدادات الذاكرة المؤقتة)**
- **عرض حجم الذاكرة الحالي** - يظهر حجم ملفات الذاكرة المؤقتة
- **زر مسح الذاكرة** - لتنظيف الذاكرة المؤقتة

### 🎨 **المميزات التقنية:**

#### **واجهة تفاعلية:**
- **قائمة جانبية للتنقل** - للانتقال السريع بين الأقسام
- **إظهار/إخفاء ديناميكي** - إعدادات التخزين تظهر حسب النوع المختار
- **تصميم متجاوب** - يعمل على جميع أحجام الشاشات
- **رسائل مساعدة** - نصائح وروابط مفيدة

#### **JavaScript المضاف:**
```javascript
// إظهار/إخفاء إعدادات التخزين حسب النوع
function toggleStorageSettings() {
    var storageType = $('#storage_setting').val();
    $('.s3-settings, .wasabi-settings').hide();
    
    if (storageType === 's3') {
        $('.s3-settings').show();
    } else if (storageType === 'wasabi') {
        $('.wasabi-settings').show();
    }
}
```

### 🔧 **التعديلات على الكود:**

#### **تصحيح الصلاحيات:**
```php
// في SystemController.php
public function saveSystemSettings(Request $request)
{
    if (\Auth::user()->can('manage system settings')) {
        // تم تغيير الصلاحية من 'manage company settings' إلى 'manage system settings'
```

#### **Routes المستخدمة:**
- `GET /settings` → `SystemController@index` (للمدير العام)
- `POST /system-settings` → `SystemController@saveSystemSettings`
- `POST /payment-settings` → `SystemController@savePaymentSettings`
- `POST /storage-settings` → `SystemController@saveStorageSettings`

### 📝 **كيفية الاستخدام:**

1. **الدخول للصفحة:** `/settings` (للمدير العام فقط)
2. **تعديل الإعدادات:** املأ الحقول المطلوبة
3. **حفظ التغييرات:** اضغط على "Save Changes" في كل قسم
4. **التحقق من التحديث:** ستظهر رسالة نجاح عند الحفظ

### ⚠️ **ملاحظات مهمة:**

1. **الصلاحيات:** تتطلب صلاحية `manage system settings`
2. **المدير العام فقط:** هذه الصفحة مخصصة للمدير العام (Super Admin)
3. **إعدادات الشركة:** للشركات، استخدم الصفحة الأخرى `/settings` (company settings)
4. **النسخ الاحتياطي:** يُنصح بأخذ نسخة احتياطية قبل تغيير إعدادات التخزين

### 📋 **قائمة الإعدادات الكاملة:**

الآن صفحة إعدادات Super Admin تحتوي على **10 أقسام رئيسية**:

1. ✅ **Brand Settings** - الشعارات والألوان والعلامة التجارية
2. ✅ **System Settings** - الإعدادات الأساسية للنظام
3. ✅ **Email Settings** - إعدادات SMTP والبريد الإلكتروني
4. ✅ **Payment Settings** - بوابات الدفع (Stripe, PayPal)
5. ✅ **Storage Settings** - التخزين (محلي، AWS S3، Wasabi)
6. ✅ **Pusher Settings** - الإشعارات الفورية
7. ✅ **reCAPTCHA Settings** - الحماية والأمان
8. ✅ **SEO Settings** - محركات البحث
9. ✅ **Cookie Settings** - إعدادات GDPR
10. ✅ **Cache Settings** - إدارة الذاكرة المؤقتة

### 🎯 **النتائج:**
- ✅ **جميع الإعدادات المفقودة تم إضافتها** بما في ذلك تغيير الألوان والشعارات
- ✅ **واجهة شاملة ومنظمة** تحتوي على جميع إعدادات Super Admin
- ✅ **قائمة جانبية تفاعلية** للتنقل السريع بين الأقسام
- ✅ **تفاعل ديناميكي** لإظهار/إخفاء الإعدادات المناسبة
- ✅ **تكامل كامل** مع النظام الموجود والـ routes
- ✅ **سهولة الاستخدام** مع التوجيهات والمساعدة

الآن صفحة إعدادات النظام **مكتملة 100%** وتحتوي على جميع الخيارات المطلوبة! 🎉

### 🔗 **الوصول للصفحة:**
- **URL:** `/systems` (للمدير العام فقط)
- **الصلاحية المطلوبة:** `manage system settings`
