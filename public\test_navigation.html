<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>اختبار القائمة الجانبية</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        .list-group-item {
            font-weight: 500;
            color: #495057;
            text-decoration: none;
        }
        .list-group-item:hover {
            background-color: #f8f9fa;
        }
        .list-group-item.active {
            background-color: #007bff;
            border-color: #007bff;
            color: white;
        }
        .test-section {
            min-height: 400px;
            padding: 20px;
            margin-bottom: 20px;
            border: 1px solid #dee2e6;
            border-radius: 0.375rem;
        }
    </style>
</head>
<body>
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-xl-3">
                <div class="card sticky-top" style="top:30px;">
                    <div class="list-group list-group-flush" id="useradd-sidenav">
                        <a href="#system-settings" class="list-group-item list-group-item-action border-0">
                            إعدادات النظام
                            <div class="float-end"><i class="ti ti-chevron-right">→</i></div>
                        </a>
                        <a href="#payment-settings" class="list-group-item list-group-item-action border-0">
                            إعدادات الدفع
                            <div class="float-end"><i class="ti ti-chevron-right">→</i></div>
                        </a>
                        <a href="#storage-settings" class="list-group-item list-group-item-action border-0">
                            إعدادات التخزين
                            <div class="float-end"><i class="ti ti-chevron-right">→</i></div>
                        </a>
                        <a href="#cache-settings" class="list-group-item list-group-item-action border-0">
                            إعدادات الذاكرة المؤقتة
                            <div class="float-end"><i class="ti ti-chevron-right">→</i></div>
                        </a>
                    </div>
                </div>
            </div>
            
            <div class="col-xl-9">
                <div id="system-settings" class="test-section">
                    <h3>إعدادات النظام</h3>
                    <p>هذا قسم إعدادات النظام الأساسية مثل اسم التطبيق والوصف واللغة الافتراضية.</p>
                </div>
                
                <div id="payment-settings" class="test-section">
                    <h3>إعدادات الدفع</h3>
                    <p>هذا قسم إعدادات بوابات الدفع مثل Stripe و PayPal.</p>
                </div>
                
                <div id="storage-settings" class="test-section">
                    <h3>إعدادات التخزين</h3>
                    <p>هذا قسم إعدادات التخزين المحلي و AWS S3 و Wasabi.</p>
                </div>
                
                <div id="cache-settings" class="test-section">
                    <h3>إعدادات الذاكرة المؤقتة</h3>
                    <p>هذا قسم إدارة الذاكرة المؤقتة للنظام.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
    <script>
        $(document).ready(function() {
            // Smooth scrolling for navigation links
            $('#useradd-sidenav a[href^="#"]').on('click', function(e) {
                e.preventDefault();
                var target = $(this.getAttribute('href'));
                if (target.length) {
                    $('html, body').stop().animate({
                        scrollTop: target.offset().top - 100
                    }, 1000);
                    
                    // Update active state
                    $('#useradd-sidenav a').removeClass('active');
                    $(this).addClass('active');
                }
            });
            
            // Update active navigation on scroll
            $(window).scroll(function() {
                var scrollPos = $(document).scrollTop() + 150;
                $('#useradd-sidenav a[href^="#"]').each(function() {
                    var currLink = $(this);
                    var refElement = $(currLink.attr("href"));
                    if (refElement.position() && refElement.position().top <= scrollPos && refElement.position().top + refElement.height() > scrollPos) {
                        $('#useradd-sidenav a').removeClass("active");
                        currLink.addClass("active");
                    }
                });
            });
        });
    </script>
</body>
</html>
