@extends('layouts.admin')
@section('page-title')
    {{ __('System Settings') }}
@endsection

@section('breadcrumb')
    <li class="breadcrumb-item"><a href="{{ route('dashboard') }}">{{ __('Dashboard') }}</a></li>
    <li class="breadcrumb-item">{{ __('System Settings') }}</li>
@endsection

@push('css-page')
    <link rel="stylesheet" href="{{ asset('css/summernote/summernote-bs4.css') }}">
@endpush

@push('script-page')
    <script src="{{ asset('css/summernote/summernote-bs4.js') }}"></script>
@endpush

@section('content')
    <div class="row">
        <div class="col-sm-12">
            <div class="row">
                <div class="col-xl-3">
                    <div class="card sticky-top" style="top:30px; z-index:unset;">
                        <div class="list-group list-group-flush" id="useradd-sidenav">
                            <a href="#system-settings"
                                class="list-group-item list-group-item-action border-0">{{ __('System Settings') }}
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                            <a href="#payment-settings"
                                class="list-group-item list-group-item-action border-0">{{ __('Payment Settings') }}
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                            <a href="#storage-settings"
                                class="list-group-item list-group-item-action border-0">{{ __('Storage Settings') }}
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                            <a href="#cache-settings"
                                class="list-group-item list-group-item-action border-0">{{ __('Cache Settings') }}
                                <div class="float-end"><i class="ti ti-chevron-right"></i></div>
                            </a>
                        </div>
                    </div>
                </div>

                <div class="col-xl-9">
                    <!-- System Settings -->
                    <div id="system-settings" class="card">
                        {{ Form::open(['route' => 'system.settings', 'method' => 'POST']) }}
                        <div class="card-header">
                            <h5>{{ __('System Settings') }}</h5>
                            <small class="text-muted">{{ __('Edit your system settings') }}</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        {{ Form::label('site_currency', __('Currency *'), ['class' => 'form-label']) }}
                                        {{ Form::text('site_currency', isset($settings['site_currency']) ? $settings['site_currency'] : '', ['class' => 'form-control font-style', 'required' => 'required']) }}
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        {{ Form::label('site_currency_symbol', __('Currency Symbol *'), ['class' => 'form-label']) }}
                                        {{ Form::text('site_currency_symbol', isset($settings['site_currency_symbol']) ? $settings['site_currency_symbol'] : '', ['class' => 'form-control', 'required' => 'required']) }}
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        {{ Form::label('site_currency_symbol_position', __('Currency Symbol Position'), ['class' => 'form-label']) }}
                                        {{ Form::select('site_currency_symbol_position', ['pre' => 'Pre', 'post' => 'Post'], isset($settings['site_currency_symbol_position']) ? $settings['site_currency_symbol_position'] : 'pre', ['class' => 'form-control select2']) }}
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        {{ Form::label('site_date_format', __('Date Format'), ['class' => 'form-label']) }}
                                        {{ Form::select('site_date_format', ['M j, Y' => 'Jan 1,2015', 'd-m-Y' => '01-01-2015', 'm-d-Y' => '01-01-2015', 'Y-m-d' => '2015-01-01'], isset($settings['site_date_format']) ? $settings['site_date_format'] : 'M j, Y', ['class' => 'form-control select2']) }}
                                    </div>
                                </div>
                                <div class="col-lg-6 col-md-6 col-sm-12">
                                    <div class="form-group">
                                        {{ Form::label('site_time_format', __('Time Format'), ['class' => 'form-label']) }}
                                        {{ Form::select('site_time_format', ['g:i A' => '12:01 PM', 'g:i a' => '12:01 pm', 'H:i' => '12:01'], isset($settings['site_time_format']) ? $settings['site_time_format'] : 'g:i A', ['class' => 'form-control select2']) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button class="btn-submit btn btn-primary" type="submit">
                                {{ __('Save Changes') }}
                            </button>
                        </div>
                        {{ Form::close() }}
                    </div>

                    <!-- Payment Settings -->
                    <div id="payment-settings" class="card">
                        {{ Form::open(['route' => 'payment.settings', 'method' => 'POST']) }}
                        <div class="card-header">
                            <h5>{{ __('Payment Settings') }}</h5>
                            <small class="text-muted">{{ __('Edit your payment gateway settings') }}</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        {{ Form::label('currency', __('Currency'), ['class' => 'form-label']) }}
                                        {{ Form::text('currency', isset($admin_payment_setting['currency']) ? $admin_payment_setting['currency'] : '', ['class' => 'form-control font-style', 'placeholder' => __('Enter Currency')]) }}
                                        <small class="text-xs">
                                            {{ __('Note: Add currency code as per three-letter ISO code') }}
                                            <a href="https://stripe.com/docs/currencies" target="_blank">{{ __('you can find out here') }}</a>.
                                        </small>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button class="btn-submit btn btn-primary" type="submit">
                                {{ __('Save Changes') }}
                            </button>
                        </div>
                        {{ Form::close() }}
                    </div>

                    <!-- Storage Settings -->
                    <div id="storage-settings" class="card">
                        {{ Form::open(['route' => 'storage.setting.store', 'method' => 'POST']) }}
                        <div class="card-header">
                            <h5>{{ __('Storage Settings') }}</h5>
                            <small class="text-muted">{{ __('Configure your file storage settings') }}</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        {{ Form::label('storage_setting', __('Storage Setting'), ['class' => 'form-label']) }}
                                        {{ Form::select('storage_setting', ['local' => 'Local', 's3' => 'AWS S3', 'wasabi' => 'Wasabi'], isset($settings['storage_setting']) ? $settings['storage_setting'] : 'local', ['class' => 'form-control select2']) }}
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            <button class="btn-submit btn btn-primary" type="submit">
                                {{ __('Save Changes') }}
                            </button>
                        </div>
                        {{ Form::close() }}
                    </div>

                    <!-- Cache Settings -->
                    <div id="cache-settings" class="card">
                        <div class="card-header">
                            <h5>{{ __('Cache Settings') }}</h5>
                            <small class="text-muted">{{ __('Manage system cache') }}</small>
                        </div>
                        <div class="card-body">
                            <div class="row">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <label class="form-label">{{ __('Current Cache Size') }}</label>
                                        <p class="text-muted">{{ $file_size }} {{ __('MB') }}</p>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="card-footer text-end">
                            {{ Form::open(['route' => 'cache.settings.store', 'method' => 'POST']) }}
                            <button class="btn btn-danger" type="submit">
                                {{ __('Clear Cache') }}
                            </button>
                            {{ Form::close() }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
